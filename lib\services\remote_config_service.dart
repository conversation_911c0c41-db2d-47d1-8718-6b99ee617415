import 'package:flutter/foundation.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import '../models/storage_config_model.dart';

class RemoteConfigService {
  static final FirebaseRemoteConfig _remoteConfig = FirebaseRemoteConfig.instance;
  static bool _isInitialized = false;

  // Google Drive Remote Config Keys
  static const String _googleDriveClientIdKey = 'google_drive_client_id';
  static const String _googleDriveClientSecretKey = 'google_drive_client_secret';
  static const String _googleDriveProjectIdKey = 'google_drive_project_id';
  static const String _googleDrivePrivateKeyIdKey = 'google_drive_private_key_id';
  static const String _googleDrivePrivateKeyKey = 'google_drive_private_key';
  static const String _googleDriveClientEmailKey = 'google_drive_client_email';
  static const String _googleDriveParentFolderIdKey = 'google_drive_parent_folder_id';
  static const String _googleDriveEnabledKey = 'google_drive_enabled';
  static const String _googleDriveAuthUriKey = 'google_drive_auth_uri';
  static const String _googleDriveTokenUriKey = 'google_drive_token_uri';
  static const String _googleDriveAuthProviderCertUrlKey = 'google_drive_auth_provider_cert_url';
  static const String _googleDriveClientCertUrlKey = 'google_drive_client_cert_url';
  static const String _googleDriveUniverseDomainKey = 'google_drive_universe_domain';

  // Configuration source management
  static const String _configSourceKey = 'google_drive_config_source'; // 'remote' or 'database'
  static const String _useRemoteConfigKey = 'google_drive_use_remote_config';

  // Singleton pattern
  static final RemoteConfigService _instance = RemoteConfigService._internal();
  factory RemoteConfigService() => _instance;
  RemoteConfigService._internal();

  /// Initialize remote config with Google Drive defaults
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: const Duration(hours: 1),
      ));

      // No default values - all configuration must come from Firebase Remote Config server
      // This ensures the app always uses server-side configuration without fallback values

      await _remoteConfig.fetchAndActivate();
      _isInitialized = true;
      debugPrint('Remote Config Service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Remote Config Service: $e');
      _isInitialized = false;
    }
  }

  /// Fetch and activate latest remote config
  static Future<void> fetchAndActivate() async {
    try {
      await _remoteConfig.fetchAndActivate();
      debugPrint('Remote config fetched and activated successfully');
    } catch (e) {
      debugPrint('Error fetching remote config: $e');
    }
  }

  /// Check if Google Drive is enabled via remote config
  static bool isGoogleDriveEnabled() {
    return _remoteConfig.getBool(_googleDriveEnabledKey);
  }

  /// Check if remote config should be used as primary source
  static bool useRemoteConfigAsPrimary() {
    return _remoteConfig.getBool(_useRemoteConfigKey);
  }

  /// Get configuration source preference
  static String getConfigSource() {
    return _remoteConfig.getString(_configSourceKey);
  }

  /// Set configuration source preference
  static Future<void> setConfigSource(String source) async {
    // This would typically be done through Firebase Console
    // but we can provide a method for programmatic updates if needed
    debugPrint('Configuration source should be set to: $source');
    debugPrint('Please update the "$_configSourceKey" key in Firebase Remote Config');
  }

  /// Get Google Drive configuration from remote config
  static StorageConfigModel? getGoogleDriveConfig() {
    try {
      if (!isGoogleDriveEnabled()) {
        debugPrint('Google Drive is disabled via remote config');
        return null;
      }

      final clientId = _remoteConfig.getString(_googleDriveClientIdKey);
      final clientEmail = _remoteConfig.getString(_googleDriveClientEmailKey);
      final projectId = _remoteConfig.getString(_googleDriveProjectIdKey);
      final privateKey = _remoteConfig.getString(_googleDrivePrivateKeyKey);
      final privateKeyId = _remoteConfig.getString(_googleDrivePrivateKeyIdKey);

      // Check if essential fields are available
      if (clientId.isEmpty || clientEmail.isEmpty || projectId.isEmpty || privateKey.isEmpty) {
        debugPrint('Google Drive remote config is incomplete');
        return null;
      }

      // Get additional fields
      final authUri = _remoteConfig.getString(_googleDriveAuthUriKey);
      final tokenUri = _remoteConfig.getString(_googleDriveTokenUriKey);
      final authProviderCertUrl = _remoteConfig.getString(_googleDriveAuthProviderCertUrlKey);
      final clientCertUrl = _remoteConfig.getString(_googleDriveClientCertUrlKey);
      final universeDomain = _remoteConfig.getString(_googleDriveUniverseDomainKey);

      return StorageConfigModel(
        id: 'remote_config_google_drive',
        configName: 'google_drive',
        clientId: clientId,
        clientSecret: _remoteConfig.getString(_googleDriveClientSecretKey),
        projectId: projectId,
        privateKeyId: privateKeyId,
        privateKey: privateKey,
        clientEmail: clientEmail,
        authUri: authUri.isNotEmpty ? authUri : 'https://accounts.google.com/o/oauth2/auth',
        tokenUri: tokenUri.isNotEmpty ? tokenUri : 'https://oauth2.googleapis.com/token',
        authProviderCertUrl: authProviderCertUrl.isNotEmpty
            ? authProviderCertUrl
            : 'https://www.googleapis.com/oauth2/v1/certs',
        clientCertUrl: clientCertUrl.isNotEmpty
            ? clientCertUrl
            : 'https://www.googleapis.com/robot/v1/metadata/x509/${Uri.encodeComponent(clientEmail)}',
        universeDomain: universeDomain.isNotEmpty ? universeDomain : 'googleapis.com',
        parentFolderId: _remoteConfig.getString(_googleDriveParentFolderIdKey).isEmpty
            ? null
            : _remoteConfig.getString(_googleDriveParentFolderIdKey),
        additionalSettings: {
          'config_source': 'remote_config',
          'use_remote_config': useRemoteConfigAsPrimary(),
        },
        isActive: true,
        isDefault: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'remote_config',
        updatedBy: 'remote_config',
      );
    } catch (e) {
      debugPrint('Error getting Google Drive config from remote config: $e');
      return null;
    }
  }

  /// Get specific remote config value
  static String getString(String key) {
    return _remoteConfig.getString(key);
  }

  static bool getBool(String key) {
    return _remoteConfig.getBool(key);
  }

  static int getInt(String key) {
    return _remoteConfig.getInt(key);
  }

  static double getDouble(String key) {
    return _remoteConfig.getDouble(key);
  }

  /// Check if remote config is initialized
  static bool get isInitialized => _isInitialized;

  /// Get all Google Drive related keys for debugging
  static Map<String, dynamic> getGoogleDriveDebugInfo() {
    return {
      'enabled': isGoogleDriveEnabled(),
      'use_remote_config': useRemoteConfigAsPrimary(),
      'config_source': getConfigSource(),
      'client_id': _remoteConfig.getString(_googleDriveClientIdKey).isNotEmpty ? '***' : 'empty',
      'client_email': _remoteConfig.getString(_googleDriveClientEmailKey).isNotEmpty ? '***' : 'empty',
      'project_id': _remoteConfig.getString(_googleDriveProjectIdKey).isNotEmpty ? '***' : 'empty',
      'private_key': _remoteConfig.getString(_googleDrivePrivateKeyKey).isNotEmpty ? '***' : 'empty',
      'private_key_id': _remoteConfig.getString(_googleDrivePrivateKeyIdKey).isNotEmpty ? '***' : 'empty',
      'parent_folder_id': _remoteConfig.getString(_googleDriveParentFolderIdKey).isNotEmpty ? '***' : 'empty',
      'auth_uri': _remoteConfig.getString(_googleDriveAuthUriKey).isNotEmpty ? '***' : 'default',
      'token_uri': _remoteConfig.getString(_googleDriveTokenUriKey).isNotEmpty ? '***' : 'default',
      'auth_provider_cert_url': _remoteConfig.getString(_googleDriveAuthProviderCertUrlKey).isNotEmpty ? '***' : 'default',
      'client_cert_url': _remoteConfig.getString(_googleDriveClientCertUrlKey).isNotEmpty ? '***' : 'auto-generated',
      'universe_domain': _remoteConfig.getString(_googleDriveUniverseDomainKey).isNotEmpty ? '***' : 'default',
    };
  }

  /// Get app update configuration
  static Map<String, dynamic> getUpdateConfig() {
    return {
      'latest_version': getString('latest_version'),
      'minimum_version': getString('minimum_version'),
      'update_required': getBool('update_required'),
      'update_enabled': getBool('update_enabled'),
      'maintenance_mode': getBool('maintenance_mode'),
      'update_url_android': getString('update_url_android'),
      'update_url_ios': getString('update_url_ios'),
      'update_title_ar': getString('update_title_ar'),
      'update_title_en': getString('update_title_en'),
      'force_update_message_ar': getString('force_update_message_ar'),
      'force_update_message_en': getString('force_update_message_en'),
      'maintenance_message_ar': getString('maintenance_message_ar'),
      'maintenance_message_en': getString('maintenance_message_en'),
    };
  }

  /// Get all remote config values for debugging
  static Map<String, dynamic> getAllConfigValues() {
    return {
      'google_drive': getGoogleDriveDebugInfo(),
      'app_update': getUpdateConfig(),
      'is_initialized': isInitialized,
      'last_fetch_time': _remoteConfig.lastFetchTime.toIso8601String(),
      'last_fetch_status': _remoteConfig.lastFetchStatus.toString(),
    };
  }

  /// Force refresh remote config
  static Future<bool> forceRefresh() async {
    try {
      await _remoteConfig.fetchAndActivate();
      debugPrint('Remote config force refreshed successfully');
      return true;
    } catch (e) {
      debugPrint('Error force refreshing remote config: $e');
      return false;
    }
  }
}
