import 'package:flutter/foundation.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import '../models/storage_config_model.dart';

class RemoteConfigService {
  static final FirebaseRemoteConfig _remoteConfig = FirebaseRemoteConfig.instance;
  static bool _isInitialized = false;

  // Google Drive Remote Config Keys
  static const String _googleDriveClientIdKey = 'google_drive_client_id';
  static const String _googleDriveClientSecretKey = 'google_drive_client_secret';
  static const String _googleDriveProjectIdKey = 'google_drive_project_id';
  static const String _googleDrivePrivateKeyIdKey = 'google_drive_private_key_id';
  static const String _googleDrivePrivateKeyKey = 'google_drive_private_key';
  static const String _googleDriveClientEmailKey = 'google_drive_client_email';
  static const String _googleDriveParentFolderIdKey = 'google_drive_parent_folder_id';
  static const String _googleDriveEnabledKey = 'google_drive_enabled';
  static const String _googleDriveAuthUriKey = 'google_drive_auth_uri';
  static const String _googleDriveTokenUriKey = 'google_drive_token_uri';
  static const String _googleDriveAuthProviderCertUrlKey = 'google_drive_auth_provider_cert_url';
  static const String _googleDriveClientCertUrlKey = 'google_drive_client_cert_url';
  static const String _googleDriveUniverseDomainKey = 'google_drive_universe_domain';

  // Configuration source management
  static const String _configSourceKey = 'google_drive_config_source'; // 'remote' or 'database'
  static const String _useRemoteConfigKey = 'google_drive_use_remote_config';

  // Singleton pattern
  static final RemoteConfigService _instance = RemoteConfigService._internal();
  factory RemoteConfigService() => _instance;
  RemoteConfigService._internal();

  /// Initialize remote config with Google Drive defaults
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: const Duration(hours: 1),
      ));

      // Set default values based on Firebase Remote Config settings
      await _remoteConfig.setDefaults({
        // Google Drive Service Account Configuration
        _googleDriveClientIdKey: '108645105234176565402',
        _googleDriveClientSecretKey: '',
        _googleDriveProjectIdKey: 'hm-device-errors-461100',
        _googleDrivePrivateKeyIdKey: '8983bd7fd9e8963e1a9b9288fca5af56c52477b2',
        _googleDrivePrivateKeyKey: '-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDOUh2vFGGX4r8J\\nbS+Iy5yHpTdMwPqMZIQcxDmHIe+jQe8fO2xcbrMlZpDAGCRlI+FD6KzJ71ZBua1O\\n52luoLd0Q2DWke2c6caLmpKO7tFQ/UnVXfwDYx5rbbRXb72L8f6d0iaM1igkn4ki\\n3HQ/IgbTGr/t7mygVZyhMS26PWsuw2uk9w/yTTft4MbaEETbVkVndhAXRz7A3DmX\\nEkirU9cCbjQSUZUo51k4HisNEGOCxJvdaYRfvxaWb9+kFv2tYyYUm9txKzlaWQum\\nsYEqwk+EHCEm2QhLvKZnHiS4GNC88E7LecjgFA666tjzq7tDWkjbpz8qHgmLj54n\\nv9cwTmTzAgMBAAECggEAWF4AW7wBfsG9q8ZpjRAUN17SfBvNU/M7SGzc7N1im4yC\\nY+CaLU62iQbQaY4NjIodEe5mj0jkq1rg+UIRPLI2UOQI5m3zFPllseZe7jwoR6Vs\\niafq5/J8lWuTyb1QtE9OC/91xm11HRZ9cIJ2gZHL1YAIA1rSiKuSXdHH8ZQbw5dn\\nL5mIqtLY1jS4U6bgGuxeNtgjBWky5Xa67K8OMEhMmpyrojQ+6eYe2jv3ICrwRtaD\\ng1Pyscjjie8NeIG4okN4qsbUsNWVNmaCO+PAhKWUy9HF5WsWwXZB67NAwo613Lo3\\nVjkFZgmH1ofqQxYoBdGIDynBbctaCUZor+EDbxoq+QKBgQD3M/AVl18rMQYgh4nU\\nfD7oUbut01j0OBNUXO8UxeXy0CzoIkih6Xh8XhoIh72wT/1Uf4EqUV6BPNcWMbb1\\nDKldbXTVc7Of9GCAJQwd/ZJk/HMzvI0oryUg2hBDAFOSYSinUEsI+QfkFBS1Akf+\\nC9kk5EliK/M78B9fPdH1XHs9iQKBgQDVqbwaYv8gO1CGD420loUHR6h2vo8tmn68\\nQVs5jolW+HQDDfbMxKyyDBVQZhqcdF5Pm79khigsAQLkdK0d0/LPAL6zupFgiK4v\\nwzPKlkh6Nx+GV2BqSemTzmh7ejRn9yyOIKXCHRg3LqzfkGbAmu6+bK6FieHAcg9+\\nh30B6CFLmwKBgQCiMbm88OzIcgoLHeMZP8wmsjnp+1nKI2DLYY6cmpwdjOlqBdSS\\nJ5WeRD/giO+010boQiCFm43eTgTx0vywG/akDkUyKBAbS0Rlz+j7ZHzBNcKAl54m\\nq4Q44dr5myU6TfVWkiApavv6chKEEVMbJLr8saxUdXHKSerLmsw/qMZTwQKBgD17\\nC99j4Rm7N55710DBlyHTIsyYZMawkAtHmyJiu25FK8y9NekSJsdM5s0ttDaU3ZZ2\\nsIoSeHjBCb4HPNE6bcHW6wj4hGCMs+KWfs6qk8xnFDEp71pmTknzuOje7zmDTFTR\\nXCEREii+y4vBF3AQpOYyi71tQYKIz6CPCA+GZh5FAoGAfrg6bk9eiupTDtK4m+8i\\noM5ojC2n1vi0nYD+zidF700tesBJnHMWBDcV2G4itFbG1g0uSirHZoFvrCnfsnr8\\nbxFkCUG+8xuXTS/4zHmrjZE3OCv4wGWwumiDdE5zQqXzR6OIW4HmUOmh1zK6eUHr\\nR6z75/PnskSyCnN3woU5crw=\\n-----END PRIVATE KEY-----\\n',
        _googleDriveClientEmailKey: '<EMAIL>',
        _googleDriveParentFolderIdKey: '1IWbkdV_jHH18-1eV8cYWuVVp3zbJxnUR',
        _googleDriveEnabledKey: true,
        _googleDriveAuthUriKey: 'https://accounts.google.com/o/oauth2/auth',
        _googleDriveTokenUriKey: 'https://oauth2.googleapis.com/token',
        _googleDriveAuthProviderCertUrlKey: 'https://www.googleapis.com/oauth2/v1/certs',
        _googleDriveClientCertUrlKey: 'https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40project.iam.gserviceaccount.com',
        _googleDriveUniverseDomainKey: 'googleapis.com',

        // Configuration source management
        _configSourceKey: 'remote', // Default to remote config as highest priority
        _useRemoteConfigKey: true,

        // App Update Service keys
        'latest_version': '1.0.7',
        'minimum_version': '1.0.7',
        'update_required': true,
        'update_enabled': true,
        'maintenance_mode': false,
        'update_url_android': 'https://play.google.com/store/apps/details?id=com.mohamedrady.hmdeviceerrors',
        'update_url_ios': '',
        'update_title_ar': 'تحديث مطلوب',
        'update_title_en': 'Update Required',
        'force_update_message_ar': 'يجب تحديث التطبيق للمتابعة. هذا الإصدار لم يعد مدعوماً.',
        'force_update_message_en': 'You must update the app to continue. This version is no longer supported.',
        'maintenance_message_ar': 'التطبيق تحت الصيانة حالياً. يرجى المحاولة لاحقاً.',
        'maintenance_message_en': 'The app is currently under maintenance. Please try again later.',
      });

      await _remoteConfig.fetchAndActivate();
      _isInitialized = true;
      debugPrint('Remote Config Service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Remote Config Service: $e');
      _isInitialized = false;
    }
  }

  /// Fetch and activate latest remote config
  static Future<void> fetchAndActivate() async {
    try {
      await _remoteConfig.fetchAndActivate();
      debugPrint('Remote config fetched and activated successfully');
    } catch (e) {
      debugPrint('Error fetching remote config: $e');
    }
  }

  /// Check if Google Drive is enabled via remote config
  static bool isGoogleDriveEnabled() {
    return _remoteConfig.getBool(_googleDriveEnabledKey);
  }

  /// Check if remote config should be used as primary source
  static bool useRemoteConfigAsPrimary() {
    return _remoteConfig.getBool(_useRemoteConfigKey);
  }

  /// Get configuration source preference
  static String getConfigSource() {
    return _remoteConfig.getString(_configSourceKey);
  }

  /// Set configuration source preference
  static Future<void> setConfigSource(String source) async {
    // This would typically be done through Firebase Console
    // but we can provide a method for programmatic updates if needed
    debugPrint('Configuration source should be set to: $source');
    debugPrint('Please update the "$_configSourceKey" key in Firebase Remote Config');
  }

  /// Get Google Drive configuration from remote config
  static StorageConfigModel? getGoogleDriveConfig() {
    try {
      if (!isGoogleDriveEnabled()) {
        debugPrint('Google Drive is disabled via remote config');
        return null;
      }

      final clientId = _remoteConfig.getString(_googleDriveClientIdKey);
      final clientEmail = _remoteConfig.getString(_googleDriveClientEmailKey);
      final projectId = _remoteConfig.getString(_googleDriveProjectIdKey);
      final privateKey = _remoteConfig.getString(_googleDrivePrivateKeyKey);
      final privateKeyId = _remoteConfig.getString(_googleDrivePrivateKeyIdKey);

      // Check if essential fields are available
      if (clientId.isEmpty || clientEmail.isEmpty || projectId.isEmpty || privateKey.isEmpty) {
        debugPrint('Google Drive remote config is incomplete');
        return null;
      }

      // Get additional fields
      final authUri = _remoteConfig.getString(_googleDriveAuthUriKey);
      final tokenUri = _remoteConfig.getString(_googleDriveTokenUriKey);
      final authProviderCertUrl = _remoteConfig.getString(_googleDriveAuthProviderCertUrlKey);
      final clientCertUrl = _remoteConfig.getString(_googleDriveClientCertUrlKey);
      final universeDomain = _remoteConfig.getString(_googleDriveUniverseDomainKey);

      return StorageConfigModel(
        id: 'remote_config_google_drive',
        configName: 'google_drive',
        clientId: clientId,
        clientSecret: _remoteConfig.getString(_googleDriveClientSecretKey),
        projectId: projectId,
        privateKeyId: privateKeyId,
        privateKey: privateKey,
        clientEmail: clientEmail,
        authUri: authUri.isNotEmpty ? authUri : 'https://accounts.google.com/o/oauth2/auth',
        tokenUri: tokenUri.isNotEmpty ? tokenUri : 'https://oauth2.googleapis.com/token',
        authProviderCertUrl: authProviderCertUrl.isNotEmpty
            ? authProviderCertUrl
            : 'https://www.googleapis.com/oauth2/v1/certs',
        clientCertUrl: clientCertUrl.isNotEmpty
            ? clientCertUrl
            : 'https://www.googleapis.com/robot/v1/metadata/x509/${Uri.encodeComponent(clientEmail)}',
        universeDomain: universeDomain.isNotEmpty ? universeDomain : 'googleapis.com',
        parentFolderId: _remoteConfig.getString(_googleDriveParentFolderIdKey).isEmpty
            ? null
            : _remoteConfig.getString(_googleDriveParentFolderIdKey),
        additionalSettings: {
          'config_source': 'remote_config',
          'use_remote_config': useRemoteConfigAsPrimary(),
        },
        isActive: true,
        isDefault: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'remote_config',
        updatedBy: 'remote_config',
      );
    } catch (e) {
      debugPrint('Error getting Google Drive config from remote config: $e');
      return null;
    }
  }

  /// Get specific remote config value
  static String getString(String key) {
    return _remoteConfig.getString(key);
  }

  static bool getBool(String key) {
    return _remoteConfig.getBool(key);
  }

  static int getInt(String key) {
    return _remoteConfig.getInt(key);
  }

  static double getDouble(String key) {
    return _remoteConfig.getDouble(key);
  }

  /// Check if remote config is initialized
  static bool get isInitialized => _isInitialized;

  /// Get all Google Drive related keys for debugging
  static Map<String, dynamic> getGoogleDriveDebugInfo() {
    return {
      'enabled': isGoogleDriveEnabled(),
      'use_remote_config': useRemoteConfigAsPrimary(),
      'config_source': getConfigSource(),
      'client_id': _remoteConfig.getString(_googleDriveClientIdKey).isNotEmpty ? '***' : 'empty',
      'client_email': _remoteConfig.getString(_googleDriveClientEmailKey).isNotEmpty ? '***' : 'empty',
      'project_id': _remoteConfig.getString(_googleDriveProjectIdKey).isNotEmpty ? '***' : 'empty',
      'private_key': _remoteConfig.getString(_googleDrivePrivateKeyKey).isNotEmpty ? '***' : 'empty',
      'private_key_id': _remoteConfig.getString(_googleDrivePrivateKeyIdKey).isNotEmpty ? '***' : 'empty',
      'parent_folder_id': _remoteConfig.getString(_googleDriveParentFolderIdKey).isNotEmpty ? '***' : 'empty',
      'auth_uri': _remoteConfig.getString(_googleDriveAuthUriKey).isNotEmpty ? '***' : 'default',
      'token_uri': _remoteConfig.getString(_googleDriveTokenUriKey).isNotEmpty ? '***' : 'default',
      'auth_provider_cert_url': _remoteConfig.getString(_googleDriveAuthProviderCertUrlKey).isNotEmpty ? '***' : 'default',
      'client_cert_url': _remoteConfig.getString(_googleDriveClientCertUrlKey).isNotEmpty ? '***' : 'auto-generated',
      'universe_domain': _remoteConfig.getString(_googleDriveUniverseDomainKey).isNotEmpty ? '***' : 'default',
    };
  }

  /// Get app update configuration
  static Map<String, dynamic> getUpdateConfig() {
    return {
      'latest_version': getString('latest_version'),
      'minimum_version': getString('minimum_version'),
      'update_required': getBool('update_required'),
      'update_enabled': getBool('update_enabled'),
      'maintenance_mode': getBool('maintenance_mode'),
      'update_url_android': getString('update_url_android'),
      'update_url_ios': getString('update_url_ios'),
      'update_title_ar': getString('update_title_ar'),
      'update_title_en': getString('update_title_en'),
      'force_update_message_ar': getString('force_update_message_ar'),
      'force_update_message_en': getString('force_update_message_en'),
      'maintenance_message_ar': getString('maintenance_message_ar'),
      'maintenance_message_en': getString('maintenance_message_en'),
    };
  }

  /// Get all remote config values for debugging
  static Map<String, dynamic> getAllConfigValues() {
    return {
      'google_drive': getGoogleDriveDebugInfo(),
      'app_update': getUpdateConfig(),
      'is_initialized': isInitialized,
      'last_fetch_time': _remoteConfig.lastFetchTime.toIso8601String(),
      'last_fetch_status': _remoteConfig.lastFetchStatus.toString(),
    };
  }

  /// Force refresh remote config
  static Future<bool> forceRefresh() async {
    try {
      await _remoteConfig.fetchAndActivate();
      debugPrint('Remote config force refreshed successfully');
      return true;
    } catch (e) {
      debugPrint('Error force refreshing remote config: $e');
      return false;
    }
  }
}
