{"parameters": {"latest_version": {"defaultValue": {"value": "1.0.7"}, "description": "Latest available app version", "valueType": "STRING"}, "minimum_version": {"defaultValue": {"value": "1.0.7"}, "description": "Minimum required app version for security compliance", "valueType": "STRING"}, "update_required": {"defaultValue": {"value": "true"}, "description": "Force update if current version is below minimum version", "valueType": "BOOLEAN"}, "update_enabled": {"defaultValue": {"value": "true"}, "description": "Enable or disable update checking functionality", "valueType": "BOOLEAN"}, "maintenance_mode": {"defaultValue": {"value": "false"}, "description": "Put app in maintenance mode to prevent usage", "valueType": "BOOLEAN"}, "update_url_android": {"defaultValue": {"value": "https://play.google.com/store/apps/details?id=com.mohamedrady.hmdeviceerrors"}, "description": "Google Play Store URL for Android app updates", "valueType": "STRING"}, "update_url_ios": {"defaultValue": {"value": ""}, "description": "App Store URL for iOS app updates (if applicable)", "valueType": "STRING"}, "update_title_ar": {"defaultValue": {"value": "تحديث مطلوب"}, "description": "Arabic title for update dialog", "valueType": "STRING"}, "update_title_en": {"defaultValue": {"value": "Update Required"}, "description": "English title for update dialog", "valueType": "STRING"}, "force_update_message_ar": {"defaultValue": {"value": "يجب تحديث التطبيق للمتابعة. هذا الإصدار لم يعد مدعوماً."}, "description": "Arabic message for forced update dialog", "valueType": "STRING"}, "force_update_message_en": {"defaultValue": {"value": "You must update the app to continue. This version is no longer supported."}, "description": "English message for forced update dialog", "valueType": "STRING"}, "maintenance_message_ar": {"defaultValue": {"value": "التطبيق تحت الصيانة حالياً. يرجى المحاولة لاحقاً."}, "description": "Arabic message for maintenance mode", "valueType": "STRING"}, "maintenance_message_en": {"defaultValue": {"value": "The app is currently under maintenance. Please try again later."}, "description": "English message for maintenance mode", "valueType": "STRING"}}, "parameterGroups": {"app_update_config": {"description": "Configuration parameters for app update management", "parameters": {"latest_version": {"defaultValue": {"value": "1.0.7"}}, "minimum_version": {"defaultValue": {"value": "1.0.7"}}, "update_required": {"defaultValue": {"value": "true"}}, "update_enabled": {"defaultValue": {"value": "true"}}, "maintenance_mode": {"defaultValue": {"value": "false"}}}}, "update_urls": {"description": "App store URLs for different platforms", "parameters": {"update_url_android": {"defaultValue": {"value": "https://play.google.com/store/apps/details?id=com.mohamedrady.hmdeviceerrors"}}, "update_url_ios": {"defaultValue": {"value": ""}}}}, "localized_messages": {"description": "Localized messages for different languages", "parameters": {"update_title_ar": {"defaultValue": {"value": "تحديث مطلوب"}}, "update_title_en": {"defaultValue": {"value": "Update Required"}}, "force_update_message_ar": {"defaultValue": {"value": "يجب تحديث التطبيق للمتابعة. هذا الإصدار لم يعد مدعوماً."}}, "force_update_message_en": {"defaultValue": {"value": "You must update the app to continue. This version is no longer supported."}}, "maintenance_message_ar": {"defaultValue": {"value": "التطبيق تحت الصيانة حالياً. يرجى المحاولة لاحقاً."}}, "maintenance_message_en": {"defaultValue": {"value": "The app is currently under maintenance. Please try again later."}}}}}, "conditions": [{"name": "force_update_condition", "expression": "app.version < parameters.minimum_version", "tagColor": "RED"}, {"name": "maintenance_condition", "expression": "parameters.maintenance_mode == true", "tagColor": "ORANGE"}, {"name": "update_available_condition", "expression": "app.version < parameters.latest_version && parameters.update_enabled == true", "tagColor": "BLUE"}], "version": {"versionNumber": "1", "updateTime": "2025-01-27T12:00:00Z", "updateUser": {"email": "<EMAIL>"}, "description": "Initial Remote Config setup for HM Device Errors v1.0.7 with app update management"}}