import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../utils/platform_utils.dart';
import 'remote_config_service.dart';

class AppUpdateService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  static const String _lastUpdateCheckKey = 'last_update_check';
  static const String _skipVersionKey = 'skip_version';
  static const Duration _checkInterval = Duration(hours: 12); // Check every 12 hours

  /// Initialize remote config
  static Future<void> initialize() async {
    try {
      // Use the centralized RemoteConfigService
      await RemoteConfigService.initialize();
      debugPrint('App Update Service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing App Update Service: $e');
    }
  }

  /// Check for app updates
  static Future<UpdateInfo?> checkForUpdates({bool forceCheck = false}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCheck = prefs.getInt(_lastUpdateCheckKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;

      // Check if we should skip this check
      if (!forceCheck && (now - lastCheck) < _checkInterval.inMilliseconds) {
        // Only print this message once per hour to reduce spam
        final lastPrintTime = prefs.getInt('last_skip_message_time') ?? 0;
        if ((now - lastPrintTime) > const Duration(hours: 1).inMilliseconds) {
          debugPrint('Skipping update check - too soon since last check (next check in ${Duration(milliseconds: _checkInterval.inMilliseconds - (now - lastCheck)).inHours} hours)');
          await prefs.setInt('last_skip_message_time', now);
        }
        return null;
      }

      // Update last check time
      await prefs.setInt(_lastUpdateCheckKey, now);

      // Fetch latest config
      await RemoteConfigService.fetchAndActivate();

      // Get remote config values first to check if they exist
      final latestVersion = RemoteConfigService.getString('latest_version');
      final minimumVersion = RemoteConfigService.getString('minimum_version');
      final updateEnabled = RemoteConfigService.getBool('update_enabled');
      final maintenanceMode = RemoteConfigService.getBool('maintenance_mode');
      final updateRequired = RemoteConfigService.getBool('update_required');

      // Check if essential configuration is missing from server
      if (latestVersion.isEmpty || minimumVersion.isEmpty) {
        debugPrint('Essential update configuration missing from server - latest_version: "$latestVersion", minimum_version: "$minimumVersion"');
        return null;
      }

      // Check if updates are enabled
      if (!updateEnabled) {
        debugPrint('Updates are disabled via remote config');
        return null;
      }

      // Check maintenance mode
      if (maintenanceMode) {
        final maintenanceMessageAr = RemoteConfigService.getString('maintenance_message_ar');
        final maintenanceMessageEn = RemoteConfigService.getString('maintenance_message_en');

        return UpdateInfo(
          type: UpdateType.maintenance,
          title: maintenanceMessageAr.isNotEmpty ? maintenanceMessageAr : 'التطبيق قيد الصيانة',
          message: maintenanceMessageAr.isNotEmpty ? maintenanceMessageAr : 'التطبيق قيد الصيانة حالياً. يرجى المحاولة لاحقاً.',
          titleEn: maintenanceMessageEn.isNotEmpty ? maintenanceMessageEn : 'App Under Maintenance',
          messageEn: maintenanceMessageEn.isNotEmpty ? maintenanceMessageEn : 'The app is currently under maintenance. Please try again later.',
          isRequired: true,
        );
      }

      // Get current app version
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;

      // Check if current version is below minimum
      if (_isVersionLower(currentVersion, minimumVersion)) {
        final updateTitleAr = RemoteConfigService.getString('update_title_ar');
        final updateTitleEn = RemoteConfigService.getString('update_title_en');
        final forceUpdateMessageAr = RemoteConfigService.getString('force_update_message_ar');
        final forceUpdateMessageEn = RemoteConfigService.getString('force_update_message_en');

        return UpdateInfo(
          type: UpdateType.required,
          currentVersion: currentVersion,
          latestVersion: latestVersion,
          title: updateTitleAr.isNotEmpty ? updateTitleAr : 'تحديث مطلوب',
          message: forceUpdateMessageAr.isNotEmpty ? forceUpdateMessageAr : 'يجب تحديث التطبيق للمتابعة. هذا الإصدار لم يعد مدعوماً.',
          titleEn: updateTitleEn.isNotEmpty ? updateTitleEn : 'Update Required',
          messageEn: forceUpdateMessageEn.isNotEmpty ? forceUpdateMessageEn : 'You must update the app to continue. This version is no longer supported.',
          updateUrl: _getUpdateUrl(),
          isRequired: true,
        );
      }

      // Check if update is available
      if (_isVersionLower(currentVersion, latestVersion)) {
        // Check if user has skipped this version
        final skippedVersion = prefs.getString(_skipVersionKey);
        if (!updateRequired && skippedVersion == latestVersion) {
          debugPrint('User has skipped version $latestVersion');
          return null;
        }

        final updateTitleAr = RemoteConfigService.getString('update_title_ar');
        final updateTitleEn = RemoteConfigService.getString('update_title_en');
        final forceUpdateMessageAr = RemoteConfigService.getString('force_update_message_ar');
        final forceUpdateMessageEn = RemoteConfigService.getString('force_update_message_en');

        return UpdateInfo(
          type: updateRequired ? UpdateType.required : UpdateType.optional,
          currentVersion: currentVersion,
          latestVersion: latestVersion,
          title: updateTitleAr.isNotEmpty ? updateTitleAr : 'تحديث متوفر',
          message: updateRequired
            ? (forceUpdateMessageAr.isNotEmpty ? forceUpdateMessageAr : 'يجب تحديث التطبيق للمتابعة.')
            : 'يتوفر إصدار جديد من التطبيق. يرجى التحديث للحصول على أحدث الميزات والإصلاحات.',
          titleEn: updateTitleEn.isNotEmpty ? updateTitleEn : 'Update Available',
          messageEn: updateRequired
            ? (forceUpdateMessageEn.isNotEmpty ? forceUpdateMessageEn : 'You must update the app to continue.')
            : 'A new version of the app is available. Please update to get the latest features and fixes.',
          updateUrl: _getUpdateUrl(),
          isRequired: updateRequired,
        );
      }

      debugPrint('App is up to date');
      return null;
    } catch (e) {
      debugPrint('Error checking for updates: $e');
      return null;
    }
  }

  /// Get update URL based on platform
  static String _getUpdateUrl() {
    String updateUrl = '';

    if (PlatformUtils.isAndroid) {
      updateUrl = RemoteConfigService.getString('update_url_android');
      // Fallback to Play Store if no URL provided
      if (updateUrl.isEmpty) {
        updateUrl = 'https://play.google.com/store/apps/details?id=com.mohamedrady.hmdeviceerrors';
      }
    } else if (PlatformUtils.isIOS) {
      updateUrl = RemoteConfigService.getString('update_url_ios');
      // Fallback to App Store if no URL provided
      if (updateUrl.isEmpty) {
        updateUrl = 'https://apps.apple.com/app/id123456789'; // Replace with actual App Store ID
      }
    }

    return updateUrl;
  }

  /// Compare version strings
  static bool _isVersionLower(String current, String target) {
    try {
      final currentParts = current.split('.').map(int.parse).toList();
      final targetParts = target.split('.').map(int.parse).toList();

      // Ensure both lists have the same length
      while (currentParts.length < targetParts.length) {
        currentParts.add(0);
      }
      while (targetParts.length < currentParts.length) {
        targetParts.add(0);
      }

      for (int i = 0; i < currentParts.length; i++) {
        if (currentParts[i] < targetParts[i]) {
          return true;
        } else if (currentParts[i] > targetParts[i]) {
          return false;
        }
      }
      return false; // Versions are equal
    } catch (e) {
      debugPrint('Error comparing versions: $e');
      return false;
    }
  }

  /// Launch update URL
  static Future<bool> launchUpdate(String updateUrl) async {
    try {
      if (updateUrl.isEmpty) {
        debugPrint('Update URL is empty');
        return false;
      }

      final uri = Uri.parse(updateUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        return true;
      } else {
        debugPrint('Cannot launch update URL: $updateUrl');
        return false;
      }
    } catch (e) {
      debugPrint('Error launching update URL: $e');
      return false;
    }
  }

  /// Skip current version
  static Future<void> skipVersion(String version) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_skipVersionKey, version);
      debugPrint('Skipped version: $version');
    } catch (e) {
      debugPrint('Error skipping version: $e');
    }
  }

  /// Clear skipped version
  static Future<void> clearSkippedVersion() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_skipVersionKey);
      debugPrint('Cleared skipped version');
    } catch (e) {
      debugPrint('Error clearing skipped version: $e');
    }
  }

  /// Get current app info
  static Future<Map<String, dynamic>> getAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return {
        'app_name': packageInfo.appName,
        'package_name': packageInfo.packageName,
        'version': packageInfo.version,
        'build_number': packageInfo.buildNumber,
        'build_signature': packageInfo.buildSignature,
      };
    } catch (e) {
      debugPrint('Error getting app info: $e');
      return {};
    }
  }

  /// Log update event
  static Future<void> logUpdateEvent(String event, Map<String, dynamic> data) async {
    try {
      await _firestore.collection('update_events').add({
        'event': event,
        'data': data,
        'timestamp': FieldValue.serverTimestamp(),
        'platform': PlatformUtils.operatingSystem,
        'app_info': await getAppInfo(),
      });
    } catch (e) {
      debugPrint('Error logging update event: $e');
    }
  }

  /// Get update statistics
  static Future<Map<String, dynamic>> getUpdateStatistics() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;

      // Get update events for current version
      final events = await _firestore
          .collection('update_events')
          .where('data.current_version', isEqualTo: currentVersion)
          .orderBy('timestamp', descending: true)
          .limit(100)
          .get();

      final stats = <String, int>{};
      for (final doc in events.docs) {
        final event = doc.data()['event'] as String;
        stats[event] = (stats[event] ?? 0) + 1;
      }

      return {
        'current_version': currentVersion,
        'total_events': events.docs.length,
        'event_breakdown': stats,
        'last_check': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting update statistics: $e');
      return {};
    }
  }
}

/// Update information model
class UpdateInfo {
  final UpdateType type;
  final String? currentVersion;
  final String? latestVersion;
  final String title;
  final String message;
  final String? titleEn;
  final String? messageEn;
  final String? updateUrl;
  final bool isRequired;
  final Map<String, dynamic>? metadata;

  UpdateInfo({
    required this.type,
    this.currentVersion,
    this.latestVersion,
    required this.title,
    required this.message,
    this.titleEn,
    this.messageEn,
    this.updateUrl,
    this.isRequired = false,
    this.metadata,
  });

  /// Get localized title based on language
  String getLocalizedTitle(bool isRTL) {
    if (isRTL) {
      return title;
    } else {
      return titleEn ?? title;
    }
  }

  /// Get localized message based on language
  String getLocalizedMessage(bool isRTL) {
    if (isRTL) {
      return message;
    } else {
      return messageEn ?? message;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'current_version': currentVersion,
      'latest_version': latestVersion,
      'title': title,
      'message': message,
      'title_en': titleEn,
      'message_en': messageEn,
      'update_url': updateUrl,
      'is_required': isRequired,
      'metadata': metadata,
    };
  }
}

/// Update types
enum UpdateType {
  optional,
  required,
  maintenance,
}
