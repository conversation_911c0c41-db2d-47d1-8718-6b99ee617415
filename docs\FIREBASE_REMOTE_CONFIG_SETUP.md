# 🔧 Firebase Remote Config Setup Guide

## 📋 Overview
This guide explains how to configure Firebase Remote Config for the HM Device Errors app to manage app updates, Google Drive settings, and other dynamic configurations.

## 🚀 Quick Setup

### 1. **Access Firebase Console**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `hm-device-errors-461100`
3. Navigate to **Remote Config** in the left sidebar

### 2. **Create Configuration Parameters**

#### 🔄 **App Update Parameters**

| Parameter Key | Type | Default Value | Description |
|---------------|------|---------------|-------------|
| `latest_version` | String | `"1.0.7"` | Latest available app version |
| `minimum_version` | String | `"1.0.7"` | Minimum required app version |
| `update_required` | Boolean | `true` | Force update if below minimum |
| `update_enabled` | Boolean | `true` | Enable update checking |
| `maintenance_mode` | Boolean | `false` | App maintenance mode |
| `update_url_android` | String | `"https://play.google.com/store/apps/details?id=com.mohamedrady.hmdeviceerrors"` | Android update URL |
| `update_url_ios` | String | `""` | iOS update URL (if needed) |

#### 📱 **Update Messages (Arabic)**

| Parameter Key | Type | Value |
|---------------|------|-------|
| `update_title_ar` | String | `"تحديث مطلوب"` |
| `force_update_message_ar` | String | `"يجب تحديث التطبيق للمتابعة. هذا الإصدار لم يعد مدعوماً."` |
| `maintenance_message_ar` | String | `"التطبيق تحت الصيانة حالياً. يرجى المحاولة لاحقاً."` |

#### 📱 **Update Messages (English)**

| Parameter Key | Type | Value |
|---------------|------|-------|
| `update_title_en` | String | `"Update Required"` |
| `force_update_message_en` | String | `"You must update the app to continue. This version is no longer supported."` |
| `maintenance_message_en` | String | `"The app is currently under maintenance. Please try again later."` |

## 🔧 Step-by-Step Configuration

### **Step 1: App Update Configuration**

1. **Create `latest_version` parameter:**
   - Key: `latest_version`
   - Type: String
   - Value: `1.0.7`
   - Description: "Latest available app version"

2. **Create `minimum_version` parameter:**
   - Key: `minimum_version`
   - Type: String
   - Value: `1.0.7`
   - Description: "Minimum required app version for security"

3. **Create `update_required` parameter:**
   - Key: `update_required`
   - Type: Boolean
   - Value: `true`
   - Description: "Force update if current version is below minimum"

4. **Create `update_enabled` parameter:**
   - Key: `update_enabled`
   - Type: Boolean
   - Value: `true`
   - Description: "Enable or disable update checking"

5. **Create `maintenance_mode` parameter:**
   - Key: `maintenance_mode`
   - Type: Boolean
   - Value: `false`
   - Description: "Put app in maintenance mode"

6. **Create `update_url_android` parameter:**
   - Key: `update_url_android`
   - Type: String
   - Value: `https://play.google.com/store/apps/details?id=com.mohamedrady.hmdeviceerrors`
   - Description: "Google Play Store URL for updates"

### **Step 2: Localized Messages**

1. **Arabic Update Title:**
   - Key: `update_title_ar`
   - Value: `تحديث مطلوب`

2. **English Update Title:**
   - Key: `update_title_en`
   - Value: `Update Required`

3. **Arabic Force Update Message:**
   - Key: `force_update_message_ar`
   - Value: `يجب تحديث التطبيق للمتابعة. هذا الإصدار لم يعد مدعوماً.`

4. **English Force Update Message:**
   - Key: `force_update_message_en`
   - Value: `You must update the app to continue. This version is no longer supported.`

5. **Arabic Maintenance Message:**
   - Key: `maintenance_message_ar`
   - Value: `التطبيق تحت الصيانة حالياً. يرجى المحاولة لاحقاً.`

6. **English Maintenance Message:**
   - Key: `maintenance_message_en`
   - Value: `The app is currently under maintenance. Please try again later.`

## 🎯 Usage Scenarios

### **Scenario 1: Force App Update**
```json
{
  "latest_version": "1.0.8",
  "minimum_version": "1.0.7",
  "update_required": true,
  "update_enabled": true,
  "maintenance_mode": false
}
```

### **Scenario 2: Optional Update**
```json
{
  "latest_version": "1.0.8",
  "minimum_version": "1.0.6",
  "update_required": false,
  "update_enabled": true,
  "maintenance_mode": false
}
```

### **Scenario 3: Maintenance Mode**
```json
{
  "latest_version": "1.0.7",
  "minimum_version": "1.0.7",
  "update_required": false,
  "update_enabled": false,
  "maintenance_mode": true
}
```

## 🔄 Publishing Changes

### **Step 1: Review Configuration**
1. Check all parameter values
2. Verify message translations
3. Test with different scenarios

### **Step 2: Publish**
1. Click **"Publish changes"** in Firebase Console
2. Add a description: "Update app version requirements to 1.0.7"
3. Confirm publication

### **Step 3: Verify**
1. Wait 1-2 minutes for propagation
2. Test app with different version numbers
3. Verify update dialogs appear correctly

## 🧪 Testing

### **Test Force Update:**
1. Set `minimum_version` to `"1.0.8"`
2. Set `update_required` to `true`
3. Launch app with version 1.0.7
4. Verify force update dialog appears

### **Test Maintenance Mode:**
1. Set `maintenance_mode` to `true`
2. Launch app
3. Verify maintenance message appears

### **Test Optional Update:**
1. Set `latest_version` to `"1.0.8"`
2. Set `minimum_version` to `"1.0.6"`
3. Set `update_required` to `false`
4. Launch app with version 1.0.7
5. Verify optional update notification

## 📊 Monitoring

### **Firebase Analytics**
- Track update dialog impressions
- Monitor update completion rates
- Analyze user behavior during updates

### **App Logs**
- Check Remote Config fetch status
- Monitor update check frequency
- Track error rates

## 🔒 Security Considerations

### **Version Management**
- Always test updates in staging first
- Use gradual rollouts for major updates
- Keep minimum version reasonable

### **URL Security**
- Verify update URLs are correct
- Use HTTPS for all update links
- Test links before publishing

## 🚨 Emergency Procedures

### **Rollback Configuration**
1. Go to Firebase Console
2. Navigate to Remote Config
3. Click on "Version history"
4. Select previous working version
5. Click "Rollback to this version"

### **Disable Updates**
```json
{
  "update_enabled": false,
  "update_required": false,
  "maintenance_mode": false
}
```

### **Emergency Maintenance**
```json
{
  "maintenance_mode": true,
  "maintenance_message_ar": "صيانة طارئة - سيعود التطبيق قريباً",
  "maintenance_message_en": "Emergency maintenance - app will be back soon"
}
```

## 📋 Checklist

### **Before Publishing:**
- [ ] All parameter keys are correct
- [ ] Version numbers are valid
- [ ] Update URLs are tested
- [ ] Messages are properly translated
- [ ] Test scenarios are verified

### **After Publishing:**
- [ ] Wait for propagation (1-2 minutes)
- [ ] Test with different app versions
- [ ] Verify update dialogs work
- [ ] Monitor app logs for errors
- [ ] Check user feedback

## 🔗 Related Documentation

- [Firebase Remote Config Documentation](https://firebase.google.com/docs/remote-config)
- [App Update Service Implementation](./APP_UPDATE_SERVICE.md)
- [Version Management Guide](./VERSION_MANAGEMENT.md)

---

**Note:** Always test configuration changes in a development environment before applying to production.
