import 'package:cloud_firestore/cloud_firestore.dart';
import 'attachment_model.dart';

class DeviceError {
  final String id;
  final String categoryId;
  final String manufacturer; // Deprecated - use manufacturers instead
  final List<String> manufacturers; // Support multiple manufacturers
  final String model; // Deprecated - use models instead
  final List<String> models; // Support multiple models
  final String errorCode;
  final String description;
  final String solution;
  final List<String> imageUrls; // Deprecated - use attachments instead
  final List<AttachmentModel> attachments;
  final DateTime createdAt;
  final String createdBy;
  final DateTime? updatedAt;
  final String? updatedBy;
  final bool isFavorite;
  final List<String> favoriteUsers;

  DeviceError({
    required this.id,
    required this.categoryId,
    required this.manufacturer, // Keep for backward compatibility
    this.manufacturers = const [], // Support multiple manufacturers
    this.model = '', // Keep for backward compatibility
    this.models = const [], // Support multiple models
    required this.errorCode,
    required this.description,
    required this.solution,
    this.imageUrls = const [],
    this.attachments = const [],
    required this.createdAt,
    required this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.isFavorite = false,
    this.favoriteUsers = const [],
  });

  /// Get all manufacturers (backward compatibility helper)
  List<String> get allManufacturers {
    if (manufacturers.isNotEmpty) {
      return manufacturers;
    } else if (manufacturer.isNotEmpty) {
      return [manufacturer];
    }
    return [];
  }

  factory DeviceError.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Parse attachments
    List<AttachmentModel> attachments = [];
    if (data['attachments'] != null) {
      attachments = (data['attachments'] as List)
          .map((attachment) => AttachmentModel.fromMap(
              attachment as Map<String, dynamic>,
              attachment['id'] ?? ''))
          .toList();
    }

    return DeviceError(
      id: doc.id,
      categoryId: data['categoryId'] ?? '',
      manufacturer: data['manufacturer'] ?? '',
      manufacturers: List<String>.from(data['manufacturers'] ?? []),
      model: data['model'] ?? '',
      models: List<String>.from(data['models'] ?? []),
      errorCode: data['errorCode'] ?? '',
      description: data['description'] ?? '',
      solution: data['solution'] ?? '',
      imageUrls: List<String>.from(data['imageUrls'] ?? []),
      attachments: attachments,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
      updatedBy: data['updatedBy'],
      favoriteUsers: List<String>.from(data['favoriteUsers'] ?? []),
      isFavorite: false, // This will be set by the provider based on current user
    );
  }

  factory DeviceError.fromMap(Map<String, dynamic> data, String id) {
    // Parse attachments
    List<AttachmentModel> attachments = [];
    if (data['attachments'] != null) {
      attachments = (data['attachments'] as List)
          .map((attachment) => AttachmentModel.fromMap(
              attachment as Map<String, dynamic>,
              attachment['id'] ?? ''))
          .toList();
    }

    return DeviceError(
      id: id,
      categoryId: data['categoryId'] ?? '',
      manufacturer: data['manufacturer'] ?? '',
      manufacturers: List<String>.from(data['manufacturers'] ?? []),
      model: data['model'] ?? '',
      models: List<String>.from(data['models'] ?? []),
      errorCode: data['errorCode'] ?? '',
      description: data['description'] ?? '',
      solution: data['solution'] ?? '',
      imageUrls: List<String>.from(data['imageUrls'] ?? []),
      attachments: attachments,
      createdAt: data['createdAt'] is Timestamp
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.tryParse(data['createdAt'] ?? '') ?? DateTime.now(),
      createdBy: data['createdBy'] ?? '',
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] is Timestamp
              ? (data['updatedAt'] as Timestamp).toDate()
              : DateTime.tryParse(data['updatedAt']))
          : null,
      updatedBy: data['updatedBy'],
      favoriteUsers: List<String>.from(data['favoriteUsers'] ?? []),
      isFavorite: false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'categoryId': categoryId,
      'manufacturer': manufacturer, // Keep for backward compatibility
      'manufacturers': manufacturers, // Support multiple manufacturers
      'model': model, // Keep for backward compatibility
      'models': models, // Support multiple models
      'errorCode': errorCode,
      'description': description,
      'solution': solution,
      'imageUrls': imageUrls,
      'attachments': attachments.map((attachment) => attachment.toMap()).toList(),
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'updatedBy': updatedBy,
      'favoriteUsers': favoriteUsers,
    };
  }

  DeviceError copyWith({
    String? id,
    String? categoryId,
    String? manufacturer,
    List<String>? manufacturers,
    String? model,
    List<String>? models,
    String? errorCode,
    String? description,
    String? solution,
    List<String>? imageUrls,
    List<AttachmentModel>? attachments,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isFavorite,
    List<String>? favoriteUsers,
  }) {
    return DeviceError(
      id: id ?? this.id,
      categoryId: categoryId ?? this.categoryId,
      manufacturer: manufacturer ?? this.manufacturer,
      manufacturers: manufacturers ?? this.manufacturers,
      model: model ?? this.model,
      models: models ?? this.models,
      errorCode: errorCode ?? this.errorCode,
      description: description ?? this.description,
      solution: solution ?? this.solution,
      imageUrls: imageUrls ?? this.imageUrls,
      attachments: attachments ?? this.attachments,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      isFavorite: isFavorite ?? this.isFavorite,
      favoriteUsers: favoriteUsers ?? this.favoriteUsers,
    );
  }

  /// Get all images (from both imageUrls and attachments)
  List<String> get allImageUrls {
    final images = <String>[];
    images.addAll(imageUrls);
    images.addAll(attachments
        .where((attachment) => attachment.isImage)
        .map((attachment) => attachment.url));
    return images;
  }

  /// Get all videos
  List<AttachmentModel> get videoAttachments {
    return attachments.where((attachment) => attachment.isVideo).toList();
  }

  /// Get all documents
  List<AttachmentModel> get documentAttachments {
    return attachments.where((attachment) => attachment.isDocument).toList();
  }

  /// Get total attachment count
  int get totalAttachmentCount {
    return imageUrls.length + attachments.length;
  }

  /// Check if error has any attachments
  bool get hasAttachments {
    return imageUrls.isNotEmpty || attachments.isNotEmpty;
  }

  /// Get all models (both old single model and new multiple models)
  List<String> get allModels {
    final allModelsList = <String>[];

    // Add the old single model if it exists and is not empty
    if (model.isNotEmpty) {
      allModelsList.add(model);
    }

    // Add all models from the new models list
    allModelsList.addAll(models);

    // Remove duplicates and return
    return allModelsList.toSet().toList();
  }

  /// Get display text for models
  String get modelsDisplayText {
    final allModelsList = allModels;
    if (allModelsList.isEmpty) {
      return '';
    } else if (allModelsList.length == 1) {
      return allModelsList.first;
    } else {
      return allModelsList.join(', ');
    }
  }
}
